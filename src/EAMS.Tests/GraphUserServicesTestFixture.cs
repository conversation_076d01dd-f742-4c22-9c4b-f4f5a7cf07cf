using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Services;
using Moq;
using Xunit;
using Microsoft.Graph;
using Microsoft.Graph.Me;
using GraphUser = Microsoft.Graph.Models.User;

namespace EAMS.Tests;

    public class GraphUserServicesTestFixture
    {
        private Mock<IGraphService> _mockGraphService;
        private Mock<IUserRepository> _mockUserRepository;
        private GraphUser _user; 
        
        public GraphUserServicesTestFixture()
        {
            // Mock GraphServiceClient
            this._mockGraphService = new Mock<IGraphService>();
            this._user = new GraphUser()
            {
                Id = Guid.NewGuid().ToString(),
                DisplayName = "John Doe",
                GivenName = "John",
                Surname = "Doe",
                Mail = "<EMAIL>"
            };
            this._mockGraphService
                .Setup(x => x.GetCurrentLoginUserAsync())
                .ReturnsAsync(this._user);
            
            // Mock Repository
            this._mockUserRepository = new Mock<IUserRepository>();
        }
        
        [Fact]
        public async Task GetCurrentLoginUserAsyncTest_Should_Retrieve_Additional_Data_From_UserRepo()
        {
            // arrange
            var dbUser = new User()
            {
                Id = Guid.Parse(_user.Id),
                Position = "Admin"
            };
            this._mockUserRepository
                .Setup(x => x.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(dbUser);
            
            var targetService = new UserService(this._mockGraphService.Object, this._mockUserRepository.Object);
            
            // act
            var result = await targetService.GetCurrentLoginUserAsync();
            
            // assert
            this._mockUserRepository.Verify(repo => repo.GetByIdAsync(It.IsAny<Guid>()), Times.Once);
            Assert.Equal(result.Id,  Guid.Parse(this._user.Id));
            Assert.Equal(result.GraphUser, _user);
            Assert.Equal(result.Position, dbUser.Position);
        }
    }
