using System.Linq.Expressions;
using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Services;
using Moq;
using Xunit;
using Microsoft.Graph;
using Microsoft.Graph.Me;
using Microsoft.Graph.Models;
using GraphUser = Microsoft.Graph.Models.User;
using User = EAMS.Domain.Entities.User;

namespace EAMS.Tests;

public class UserServicesTestFixture
{
    private Mock<IGraphService> _mockGraphService;
    private Mock<IUserRepository> _mockUserRepository;
    private Mock<IUserInvitationRepository> _mockUserInvitationRepository;
    private Mock<IOrganisationRepository> _mockOrganisationRepository;
    private GraphUser _user;
    private Group _usersGroup;
    private Organisation _organisation;
    private UserService _targetService;
    
    public UserServicesTestFixture()
    {
        // Mock GraphServiceClient
        this._mockGraphService = new Mock<IGraphService>();
        this._user = new GraphUser()
        {
            Id = Guid.NewGuid().ToString(),
            DisplayName = "<PERSON> Do<PERSON>",
            GivenName = "John",
            Surname = "<PERSON><PERSON>",
            Mail = "<EMAIL>"
        };
        this._mockGraphService
            .Setup(x => x.GetCurrentLoginUserAsync())
            .ReturnsAsync(this._user);

        this._usersGroup = new Group()
        {
            Id = Guid.NewGuid().ToString(),
            DisplayName = "Users"
        };
        
        this._mockGraphService
            .Setup(x => x.GetGroupByName("Users"))
            .ReturnsAsync(this._usersGroup);
        
        // Mock Repository
        this._mockUserRepository = new Mock<IUserRepository>();
        this._mockUserInvitationRepository = new Mock<IUserInvitationRepository>();
        this._mockOrganisationRepository = new Mock<IOrganisationRepository>();
        
        // Mock Organisation
        _organisation = new Organisation()
        {
            Id = Guid.NewGuid(),
            Name = "McAuleys",
        };
        
        this._mockOrganisationRepository
            .Setup(x => x.GetByIdAsync(_organisation.Id))
            .ReturnsAsync(this._organisation);
        
        this._targetService = new UserService(this._mockGraphService.Object,
            this._mockUserRepository.Object, 
            this._mockUserInvitationRepository.Object,
            this._mockOrganisationRepository.Object);
    }

    [Fact]
    public async Task Test_GetCurrentLoginUserAsync_Should_Retrieve_Additional_Data_From_UserRepo()
    {
        // arrange
        var dbUser = new User()
        {
            Id = Guid.Parse(_user.Id),
            Position = "Admin"
        };
        this._mockUserRepository
            .Setup(x => x.GetByIdAsync(It.IsAny<Guid>())).ReturnsAsync(dbUser);


        // act
        var result = await _targetService.GetCurrentLoginUserAsync();

        // assert
        this._mockUserRepository.Verify(repo => repo.GetByIdAsync(It.IsAny<Guid>()), Times.Once);
        Assert.Equal(result.Id, Guid.Parse(this._user.Id));
        Assert.Equal(result.GraphUser, _user);
        Assert.Equal(result.Position, dbUser.Position);
    }

    [Fact]
    public async Task Test_GetCurrentLoginUserAsync_UserDoesNotExistsInUserTable_Should_Check_Existing_Invitation_AndCreate_NewUserRecord()
    {
        // arrange
        this._mockUserInvitationRepository
            .Setup(repo => repo.GetAllAsync(It.IsAny<Expression<Func<UserInvitation, bool>>>(),
                It.IsAny<Expression<Func<UserInvitation, object>>>()))
            .ReturnsAsync(new List<UserInvitation>() {new UserInvitation()
            {
                Id = Guid.NewGuid(),
                TargetOrganisationId = _organisation.Id,
                TargetOrganisation = _organisation,
            }});
        
        // act
        var result = await _targetService.GetCurrentLoginUserAsync();
        
        // assert
        Assert.NotNull(result);
        Assert.Equal(this._organisation.Id, result.OrganisationId);
        this._mockUserRepository.Verify(repo => repo.AddAsync(It.IsAny<User>()), Times.Once);
    }

    [Fact]
    public async Task Test_CreateInvitationAsync_Should_AddUserToGroup_CreateNewUserInvitation_ReturnsValidId()
    {
        // arrange
        var userInvitation = new UserInvitation()
        {
            InvitedUserEmailAddress = "<EMAIL>",
            TargetOrganisationId = this._organisation.Id,
            Roles = "Users"
        };
        
        Invitation mockInvitation = new Invitation()
        {
            Id = Guid.NewGuid().ToString(),
            InvitedUser = new GraphUser()
            {
                Id = Guid.NewGuid().ToString()
            }
        };
        
        _mockGraphService
            .Setup(x => x.InviteUser(userInvitation.InvitedUserEmailAddress, It.IsAny<string>()))
            .ReturnsAsync(mockInvitation);
        
        // act
        var result = await _targetService.CreateInvitationAsync(userInvitation);

        // assert
        Assert.Equal(Guid.Parse(mockInvitation.Id), result.Id);
        this._mockUserInvitationRepository.Verify(repo => repo.AddAsync(It.IsAny<UserInvitation>()), Times.Once);
        this._mockGraphService.Verify(repo => repo.GetCurrentLoginUserAsync(),  Times.Once);
        this._mockGraphService.Verify(repo => repo.GetGroupByName(userInvitation.Roles), Times.Once);
        this._mockGraphService.Verify(repo => repo.AddUserToGroup(Guid.Parse(mockInvitation.InvitedUser.Id),
            Guid.Parse(_usersGroup.Id)), Times.Once);
    }

    [Fact]
    public async Task Test_CreateInvitationAsync_WithExistingInvitation_ShouldResendInvitation_AndUpdateInvitationRecord()
    {
        // arrange
        GraphUser userToInvite = new GraphUser()
        {
            Id = Guid.NewGuid().ToString(),
            Mail = "<EMAIL>"
        };
        
        UserInvitation existingInvitation = new UserInvitation()
        {
            Id = Guid.NewGuid(),
            TargetOrganisationId = _organisation.Id,
            InvitedByUserId = Guid.Parse(_user.Id),
            InvitedUserId = Guid.Parse(userToInvite.Id),
            InvitedUserEmailAddress = userToInvite.Mail,
        };
        
        Invitation mockInvitation = new Invitation()
        {
            Id = existingInvitation.Id.ToString(),
            InvitedUser = new GraphUser()
            {
                Id = userToInvite.Id
            }
        };
        
        this._mockUserInvitationRepository.Setup(repo => repo.GetByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(existingInvitation);
        
        this._mockGraphService
            .Setup(x => x.InviteUser(userToInvite.Mail, It.IsAny<string>()))
            .ReturnsAsync(mockInvitation);
        
        // act
        var result = await _targetService.CreateInvitationAsync(existingInvitation);
        
        // assert
        Assert.Equal(existingInvitation.Id, result.Id);
        _mockUserInvitationRepository.Verify(repo => repo.UpdateAsync(It.IsAny<UserInvitation>()), Times.Once);
        _mockGraphService.Verify(repo => repo.AddUserToGroup(It.IsAny<Guid>(), It.IsAny<Guid>()), Times.Never);
    }
}