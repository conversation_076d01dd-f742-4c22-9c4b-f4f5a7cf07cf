﻿// <auto-generated />
using System;
using EAMS.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;

#nullable disable

namespace EAMS.Infrastructure.Migrations
{
    [DbContext(typeof(EamsDbContext))]
    [Migration("20250908115314_UserInvitationTableUpdate")]
    partial class UserInvitationTableUpdate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("AccommodationAmenityOptions", b =>
                {
                    b.Property<long>("AccommodationsId")
                        .HasColumnType("bigint");

                    b.Property<long>("AmenityOptionsId")
                        .HasColumnType("bigint");

                    b.HasKey("AccommodationsId", "AmenityOptionsId");

                    b.HasIndex("AmenityOptionsId");

                    b.ToTable("AccommodationAmenities", (string)null);
                });

            modelBuilder.Entity("AccommodationOrganisation", b =>
                {
                    b.Property<long>("AccommodationsId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("OrganisationsId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("AccommodationsId", "OrganisationsId");

                    b.HasIndex("OrganisationsId");

                    b.ToTable("OrgAccommodation", (string)null);
                });

            modelBuilder.Entity("EAMS.Domain.Entities.Accommodation", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<int>("AccommodationType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("Density")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DiscardedAt")
                        .HasColumnType("datetime2");

                    b.PrimitiveCollection<string>("Duration")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Inactive")
                        .HasColumnType("bit");

                    b.Property<Point>("Location")
                        .HasColumnType("geography");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Postcode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Region")
                        .HasColumnType("int");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StreetLine1")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StreetLine2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Suburb")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Website")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Accommodations");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.Amenity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<int>("AmenityType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("HelpText")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Amenities");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.AmenityOptions", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("AmenityId")
                        .HasColumnType("bigint");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DisplayText")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AmenityId");

                    b.ToTable("AmenityOptions");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.Organisation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ClientGroup")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DiscardedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("KeyContactEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("KeyContactName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("KeyContactPhone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ParentOrgId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Postcode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Service")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StreetLine1")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StreetLine2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Suburb")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ParentOrgId");

                    b.ToTable("Organisations");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DiscardedAt")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("OrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Position")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("OrganisationId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.UserInvitation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("InviteRedirectUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("InvitedByUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("InvitedUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Roles")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("TargetOrganisationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("InvitedByUserId");

                    b.HasIndex("TargetOrganisationId");

                    b.ToTable("UserInvitations");
                });

            modelBuilder.Entity("AccommodationAmenityOptions", b =>
                {
                    b.HasOne("EAMS.Domain.Entities.Accommodation", null)
                        .WithMany()
                        .HasForeignKey("AccommodationsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EAMS.Domain.Entities.AmenityOptions", null)
                        .WithMany()
                        .HasForeignKey("AmenityOptionsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("AccommodationOrganisation", b =>
                {
                    b.HasOne("EAMS.Domain.Entities.Accommodation", null)
                        .WithMany()
                        .HasForeignKey("AccommodationsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EAMS.Domain.Entities.Organisation", null)
                        .WithMany()
                        .HasForeignKey("OrganisationsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("EAMS.Domain.Entities.AmenityOptions", b =>
                {
                    b.HasOne("EAMS.Domain.Entities.Amenity", "Amenity")
                        .WithMany("AmenityOptions")
                        .HasForeignKey("AmenityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Amenity");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.Organisation", b =>
                {
                    b.HasOne("EAMS.Domain.Entities.Organisation", "ParentOrg")
                        .WithMany("ChildOrganisations")
                        .HasForeignKey("ParentOrgId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentOrg");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.User", b =>
                {
                    b.HasOne("EAMS.Domain.Entities.Organisation", "Organisation")
                        .WithMany("Users")
                        .HasForeignKey("OrganisationId");

                    b.Navigation("Organisation");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.UserInvitation", b =>
                {
                    b.HasOne("EAMS.Domain.Entities.User", "InvitedByUser")
                        .WithMany("InvitationsSent")
                        .HasForeignKey("InvitedByUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EAMS.Domain.Entities.Organisation", "TargetOrganisation")
                        .WithMany("UserInvitations")
                        .HasForeignKey("TargetOrganisationId");

                    b.Navigation("InvitedByUser");

                    b.Navigation("TargetOrganisation");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.Amenity", b =>
                {
                    b.Navigation("AmenityOptions");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.Organisation", b =>
                {
                    b.Navigation("ChildOrganisations");

                    b.Navigation("UserInvitations");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("EAMS.Domain.Entities.User", b =>
                {
                    b.Navigation("InvitationsSent");
                });
#pragma warning restore 612, 618
        }
    }
}
