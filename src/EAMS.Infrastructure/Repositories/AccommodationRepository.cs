using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using EAMS.Domain.Interfaces;
using EAMS.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;

namespace EAMS.Infrastructure.Repositories
{
    public class AccommodationRepository : Repository<Accommodation, Int64>, IAccommodationRepository
    {
        public AccommodationRepository(EamsDbContext context) : base(context)
        {
        }
    }
}
