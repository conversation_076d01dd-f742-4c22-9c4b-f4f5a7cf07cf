using AutoMapper;
using EAMS.API.DTOs;
using EAMS.Domain.Entities;
using NetTopologySuite.Geometries;

namespace EAMS.API.Mappings;

public class AccommodationProfile : Profile
{
    public AccommodationProfile()
    {
        // Accommodation to AccommodationDto mapping
        CreateMap<Accommodation, AccommodationDto>()
            .ForMember(dest => dest.Latitude, opt => opt.MapFrom(src =>
                src.Location.HasValue ? src.Location.Value.Latitude : (double?)null))
            .ForMember(dest => dest.Longitude, opt => opt.MapFrom(src =>
                src.Location.HasValue ? src.Location.Value.Longitude : (double?)null));


        // AccommodationDto to Accommodation mapping
        CreateMap<AccommodationDto, Accommodation>()
            .ForMember(dest => dest.Location, opt => opt.MapFrom(src =>
                src.Latitude.HasValue && src.Longitude.HasValue
                    ? GeoPoint.Create(src.Latitude.Value, src.Longitude.Value)
                    : (GeoPoint?)null));

        // Amenity mapping
        CreateMap<Amenity, AmenityDto>().ReverseMap();

        // AmenityOptions mapping
        CreateMap<AmenityOptions, AmenityOptionsDto>().ReverseMap();
    }
}
