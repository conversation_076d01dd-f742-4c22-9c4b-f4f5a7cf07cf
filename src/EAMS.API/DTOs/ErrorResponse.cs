namespace EAMS.API.DTOs;

/// <summary>
/// Standardized error response model for all API errors
/// </summary>
public class ErrorResponse
{
    /// <summary>
    /// The main error message
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Additional error details (optional)
    /// </summary>
    public string? Details { get; set; }

    /// <summary>
    /// Correlation ID for request tracking
    /// </summary>
    public string CorrelationId { get; set; } = string.Empty;

    /// <summary>
    /// HTTP status code
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    /// Validation errors (for 400 Bad Request responses)
    /// </summary>
    public Dictionary<string, string[]>? ValidationErrors { get; set; }

    /// <summary>
    /// Timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
