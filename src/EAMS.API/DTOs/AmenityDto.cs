using System.ComponentModel.DataAnnotations;
using EAMS.Domain.Entities.Enums;

namespace EAMS.API.DTOs;

public class AmenityDto
{
    public Int64 Id { get; set; }

    [Required(ErrorMessage = "Name is required")]
    [StringLength(200, ErrorMessage = "Name cannot exceed 200 characters")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Amenity type is required")]
    public AmenityType AmenityType { get; set; }

    [Required(ErrorMessage = "Help text is required")]
    [StringLength(500, ErrorMessage = "Help text cannot exceed 500 characters")]
    public string HelpText { get; set; } = string.Empty;
}
