using System.ComponentModel.DataAnnotations;

namespace EAMS.API.DTOs;

public class InvitationDto
{
    [Required]
    [EmailAddress]
    public string InvitedUserEmailAddress { get; set; } // email address of the user you are inviting

    [Required]
    public Guid TargetOrganisationId { get; set; } // Target organisation Id that user will belong to

    [Required]
    public string Roles { get; set; }

    public string? InviteRedirectUrl { get; set; } // App Url where user will be redirected in the first time login
}