using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Exceptions;

namespace EAMS.Domain.Services;

public class OrganisationService : IOrganisationService
{
    private readonly IOrganisationRepository _organisationRepository;

    public OrganisationService(IOrganisationRepository organisationRepository)
    {
        _organisationRepository = organisationRepository;
    }

    public async Task<IEnumerable<Organisation>> GetAll()
    {
        return await _organisationRepository.GetAllAsync();
    }

    public async Task<Organisation?> GetById(Guid id)
    {
        return await _organisationRepository.GetByIdAsync(id);
    }

    public async Task<Organisation> Create(Organisation organisation)
    {
        // Set timestamps for new entity
        organisation.CreatedAt = DateTime.UtcNow;
        organisation.UpdatedAt = DateTime.UtcNow;

        // AddAsync returns void and handles SaveChanges internally
        await _organisationRepository.AddAsync(organisation);

        // Return the organisation with its generated ID
        return organisation;
    }

    public async Task<Organisation> Update(Organisation organisation)
    {
        // Check if organisation exists first
        var existingOrganisation = await _organisationRepository.GetByIdAsync(organisation.Id);
        if (existingOrganisation == null)
        {
            throw new EntityNotFoundException("Organisation", organisation.Id);
        }

        // Update timestamp
        organisation.UpdatedAt = DateTime.UtcNow;
        // Preserve original creation timestamp
        organisation.CreatedAt = existingOrganisation.CreatedAt;

        // UpdateAsync returns void and handles SaveChanges internally
        await _organisationRepository.UpdateAsync(organisation);

        return organisation;
    }

    public async Task<bool> Delete(Guid id)
    {
        // Check if organisation exists first
        var exists = await _organisationRepository.GetByIdAsync(id);
        if (exists == null)
        {
            return false;
        }

        // DeleteAsync returns void and handles SaveChanges internally
        await _organisationRepository.DeleteAsync(id);

        return true;
    }
}
