using EAMS.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace EAMS.Domain.Services;

public class GraphService : IGraphService
{
    private readonly GraphServiceClient _graphClient;
    private readonly ILogger<GraphService> _logger;

    public GraphService(GraphServiceClient graphClient, ILogger<GraphService> logger)
    {
        _graphClient = graphClient;
        _logger = logger;
    }

    public async Task<User?> GetCurrentLoginUserAsync()
    {
        var graphUser = await _graphClient.Me.GetAsync(requestConfiguration =>
        {
            requestConfiguration.QueryParameters.Select = new string[] { "displayName", "givenName", "surname", "userPrincipalName", "id" };
        });
        return graphUser;
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        var searchResult = await _graphClient.Users.GetAsync(requestConfiguration =>
        {
            requestConfiguration.QueryParameters.Filter = $"mail eq 'email'";
            requestConfiguration.QueryParameters.Select = new string[] { "displayName", "givenName", "surname", "userPrincipalName", "id" };
        });

        if (searchResult.Value != null && searchResult.Value.Count > 0)
        {
            return searchResult.Value.First();
        }

        return null;
    }

    public Task<Invitation?> InviteUser(string email, string redirectUrl)
    {
        var invitation = new Invitation
        {
            InvitedUserEmailAddress = email,
            InviteRedirectUrl = redirectUrl,
            SendInvitationMessage = true,
            InvitedUserMessageInfo = new InvitedUserMessageInfo
            {
                // TODO: Store message body in the database settings table.
                CustomizedMessageBody = "You are invited to use EAMS."
            }
        };

        return _graphClient.Invitations.PostAsync(invitation);
    }

    public async Task<Group?> GetGroupByName(string groupName)
    {
        try
        {
            var groups = await _graphClient.Groups.GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter = $"displayName eq '{groupName}'";
            });

            // Return the first group found 
            return groups.Value.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error when getting group by name");
            throw;
        }
    }

    public async Task AddUserToGroup(Guid userId, Guid groupId)
    {
        var requestBody = new ReferenceCreate
        {
            OdataId = $"https://graph.microsoft.com/v1.0/directoryObjects/{userId}"
        };

        await _graphClient.Groups[groupId.ToString()].Members.Ref.PostAsync(requestBody);
    }
}