﻿using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using GraphUser = Microsoft.Graph.Models.User;
using User = EAMS.Domain.Entities.User;

namespace EAMS.Domain.Services;

public class UserService : IUserService
{
    private readonly IGraphService _graphService;
    private readonly IUserRepository _userRepository;
    private readonly IUserInvitationRepository _userInvitationRepository;
    private readonly IOrganisationRepository _organisationRepository;

    public UserService(IGraphService graphClient, IUserRepository userRepository,
        IUserInvitationRepository userInvitationRepository, IOrganisationRepository organisationRepository)
    {
        _graphService = graphClient;
        _userRepository = userRepository;
        _userInvitationRepository = userInvitationRepository;
        _organisationRepository = organisationRepository;
    }

    public async Task<User?> GetCurrentLoginUserAsync()
    {
        var graphUser = await _graphService.GetCurrentLoginUserAsync();

        if (graphUser is not null && !string.IsNullOrEmpty(graphUser.Id))
        {
            var userId = Guid.Parse(graphUser.Id);
            var user = await _userRepository.GetByIdAsync(userId);

            if (user is not null)
            {
                user.GraphUser = graphUser;
                return user;
            }
            else
            {
                // check if the any invitation for this user using query invitedUserId == userId, include target organisation navigation object
                var invitationResults = await _userInvitationRepository
                    .GetAllAsync(ui => ui.InvitedUserId == userId, ui => ui.TargetOrganisation);
                var existingInvitation = invitationResults.FirstOrDefault();

                // create a new user in the database.
                var newUser = new User
                {
                    Id = userId,
                    OrganisationId = existingInvitation?.TargetOrganisationId ?? null,
                };

                await _userRepository.AddAsync(newUser);
                newUser.GraphUser = graphUser;
                return newUser;
            }
        }

        return null;
    }

    public async Task<UserInvitation?> CreateInvitationAsync(UserInvitation userInvitation)
    {
        // check if the targetOrganisationId exists
        var organisation = await _organisationRepository.GetByIdAsync(userInvitation.TargetOrganisationId.Value);
        if (organisation == null)
            throw new InvalidDataException("TargetOrganisationId does not exists");

        // Convert newInvitation into Invitation and call GraphService.CreateInvitation
        var invitation =
            await _graphService.InviteUser(userInvitation.InvitedUserEmailAddress, userInvitation.InviteRedirectUrl);

        // Convert invitation to UserInvitation class update Invitation to database
        if (invitation is not null && !string.IsNullOrEmpty(invitation.Id))
        {
            userInvitation.Id = Guid.Parse(invitation.Id);
            userInvitation.InvitedUserId = !string.IsNullOrEmpty(invitation.InvitedUser?.Id)
                ? Guid.Parse(invitation.InvitedUser.Id)
                : Guid.Empty;
            var currentUser = await this.GetCurrentLoginUserAsync();
            userInvitation.InvitedByUserId = currentUser?.Id ?? Guid.Empty;

            // check if invitation exists
            var existingInvitation = await _userInvitationRepository.GetByIdAsync(userInvitation.Id);
            if (existingInvitation is null)
            {
                // Add new user to groups.
                var invitedToGroups = userInvitation.Roles.Split(',');
                foreach (var groupName in invitedToGroups)
                {
                    var group = await _graphService.GetGroupByName(groupName);
                    if (group is not null)
                    {
                        await _graphService.AddUserToGroup(Guid.Parse(invitation.InvitedUser.Id), Guid.Parse(group.Id));
                    }
                }

                // record invitation
                await _userInvitationRepository.AddAsync(userInvitation);
            }
            else
            {
                // just send another invite and update the record
                await _userInvitationRepository.UpdateAsync(userInvitation);
            }
        }

        return userInvitation;
    }
}