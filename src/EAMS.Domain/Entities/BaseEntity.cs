namespace EAMS.Domain.Entities;
public abstract class BaseEntity<T> where T : struct
{
  public T Id { get; set; }

  public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

  public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
}

public abstract class SoftDeletableEntity<T> : BaseEntity<T> where T : struct
{
    public DateTime? DiscardedAt { get; set; }
    
    public bool IsDiscarded => DiscardedAt.HasValue;
}