namespace EAMS.Domain.Entities.Enums;
public enum AccommodationType
{
    Hotel,
    ApartmentHotel,
    CaravanPark,
    Homestay,
    Hostel,
    MotelOrMotorInn,
    RoomingHouse,
    RoomingHouseUnregistered,
    ServicedApartment,
    SingleUnit,
    SRSPensionLevel,
    SRSAbovePensionLevel
}

public enum Density
{
    Low,
    Medium,
    High
}

public enum Region
{
    Barwon,
    BaysidePeninsula,
    BrimbankMelton,
    CentralHighlands,
    InnerEasternMelbourne,
    OuterEasternMelbourne,
    InnerGippsland,
    OuterGippsland,
    Goulburn,
    HumeMerriBek,
    Loddon,
    Mallee,
    MelbourneCBD,
    NorthEasternMelbourne,
    OvensMurray,
    SouthernMelbourne,
    OuterWesternDistrict,
    WesternMelbourne
}

public enum Duration
{
    SingleTermStay,
    ShortTermStay,
    LongTermStay
}

public enum AmenityType
{
    Characteristic,
    Safety,
    Amenity,
    Additional
}