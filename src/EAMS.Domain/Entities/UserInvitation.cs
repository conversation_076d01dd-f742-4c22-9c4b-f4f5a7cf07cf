using System.ComponentModel.DataAnnotations.Schema;

namespace EAMS.Domain.Entities;

public class UserInvitation : BaseEntity<Guid>
{
    public Guid InvitedUserId { get; set; }
    public Guid InvitedByUserId { get; set; }
    public Guid? TargetOrganisationId { get; set; }
    public string? InviteRedirectUrl { get; set; }
    public string? Roles { get; set; } // Comma seperated roles / group
    
    [NotMapped]
    public string InvitedUserEmailAddress { get; set; }
    
    // Navigation properties for relationships
    public User InvitedByUser { get; set; }
    public Organisation TargetOrganisation { get; set; }
}