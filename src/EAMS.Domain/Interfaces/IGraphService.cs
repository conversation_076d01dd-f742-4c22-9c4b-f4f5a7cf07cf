using System.Runtime.CompilerServices;
using Microsoft.Graph.Models;

namespace EAMS.Domain.Interfaces;

public interface IGraphService
{
    public Task<User?> GetCurrentLoginUserAsync();
    public Task<User?> GetUserByEmailAsync(string email);
    public Task<Invitation?> InviteUser(string email, string redirectUrl);
    public Task<Group?> GetGroupByName(string groupName);
    public Task AddUserToGroup(Guid  userId, Guid groupId);
}