namespace EAMS.Domain.Exceptions;

/// <summary>
/// Exception thrown when business rule validation fails
/// </summary>
public class ValidationException : DomainException
{
    public Dictionary<string, string[]> ValidationErrors { get; }

    public ValidationException(string message) : base(message)
    {
        ValidationErrors = new Dictionary<string, string[]>();
    }

    public ValidationException(string message, Dictionary<string, string[]> validationErrors) : base(message)
    {
        ValidationErrors = validationErrors ?? new Dictionary<string, string[]>();
    }

    public ValidationException(string field, string error) : base($"Validation failed for {field}")
    {
        ValidationErrors = new Dictionary<string, string[]>
        {
            { field, new[] { error } }
        };
    }
}
