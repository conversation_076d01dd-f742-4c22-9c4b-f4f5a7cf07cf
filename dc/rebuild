#!/bin/bash

OS="`uname`"
PROJECT_FOLDER=${PWD##*/}
case $OS in
  'Linux')
    PROJECT_FOLDER=$(echo ${PROJECT_FOLDER//[-._]/} | tr '[:upper:]' '[:lower:]')
    ;;
  *)
    ;;
esac

echo 'Rebuilding app...'
touch .env

pushd dc
docker compose stop
docker compose build --force-rm --progress=plain
popd

echo 'Done'
echo ''

echo 'Cleaning orphaned app images...'
docker rmi -f $(docker images -f "dangling=true" -q)
echo 'Done'
echo ''

echo 'Cleaning old app containers...'
app_container_ids=$(docker container ls -af "name=${PROJECT_FOLDER}_app*" -q)

if [[ -z "$app_container_ids" ]]; then
  echo 'No app containers found.'
else
  docker rm "$app_container_ids"
fi

echo 'Done'
echo ''
