name: Trigger auto deployment for ms-development-backend-ca

# When this action will be executed
on:
  # Automatically trigger it when detected changes in repo
  push:
    branches: 
      [ dev ]
    paths:
    - '**'
    - '.github/workflows/ms-development-backend-ca-AutoDeployTrigger-c779fa89-9913-4b00-b2e1-d345ea2dde41.yml'

  # Allow manual trigger 
  workflow_dispatch:      

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions: 
      id-token: write #This is required for requesting the OIDC JWT Token
      contents: read #Required when GH token is used to authenticate with private repo

    steps:
      - name: Checkout to the branch
        uses: actions/checkout@v2

      - name: Run EF Core Migrations
        run: |
          dotnet tool restore
          dotnet ef database update --project src/EAMS.Infrastructure --startup-project src/EAMS.API
        env:
          ConnectionStrings__DefaultConnection: ${{ secrets.MSDEVELOPMENT_DB_CONNECTION_STRING }}

      - name: Azure Login
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.MSDEVELOPMENTBACKENDCA_AZURE_CLIENT_ID }}
          tenant-id: ${{ secrets.MSDEVELOPMENTBACKENDCA_AZURE_TENANT_ID }}
          subscription-id: ${{ secrets.MSDEVELOPMENTBACKENDCA_AZURE_SUBSCRIPTION_ID }}

      - name: Build and push container image to registry
        uses: azure/container-apps-deploy-action@v2
        with:
          appSourcePath: ${{ github.workspace }}
          _dockerfilePathKey_: _dockerfilePath_
          _targetLabelKey_: _targetLabel_
          registryUrl: msdevelopmentcr.azurecr.io
          registryUsername: ${{ secrets.MSDEVELOPMENTBACKENDCA_REGISTRY_USERNAME }}
          registryPassword: ${{ secrets.MSDEVELOPMENTBACKENDCA_REGISTRY_PASSWORD }}
          containerAppName: ms-development-backend-ca
          resourceGroup: ms-development-rg
          imageToBuild: msdevelopmentcr.azurecr.io/ms-development-backend-ca:${{ github.sha }}
          _buildArgumentsKey_: |
            _buildArgumentsValues_


